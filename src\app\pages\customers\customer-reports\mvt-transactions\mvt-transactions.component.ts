import { Component, Input, Output, EventEmitter, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SharedService } from 'src/app/shared/services/shared.service';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { Transaction, TransactionType, TransactionStatus, TransactionOperationType } from 'src/app/shared/types/transaction.types';
import { ErrorHandlerService } from '../../../../shared/services/error-handler.service';
import { ErrorSeverity } from '../../../../shared/interfaces/standardized-error.interface';

@Component({
  selector: 'app-mvt-transactions',
  templateUrl: './mvt-transactions.component.html',
  styleUrls: ['./mvt-transactions.component.scss']
})
export class MvtTransactionsComponent implements On<PERSON><PERSON><PERSON>, OnDestroy {
  private readonly destroy$ = new Subject<void>();
  public readonly ITEMS_PER_PAGE = 10;

  public readonly TransactionType = TransactionType;
  public readonly TransactionStatus = TransactionStatus;
  public readonly TransactionOperationType = TransactionOperationType;

  math = Math;
  allTransactions: Transaction[] = [];
  displayTransactions: Transaction[] = [];
  isLoading = false;
  currentPage = 1;
  totalItems = 0;
  currentTransactionType: TransactionType = TransactionType.ALL;

  @Input() stakeholderWalletAddress = '';
  @Input() stakeholderName = '';
  @Input() showAllTransactions = false; // New input to control admin "view all" mode
  @Input() transactionLimit = 100; // New input to control number of transactions to fetch
  @Input() showPagination = true; // New input to control pagination display
  @Input() showViewAllButton = false; // New input to control "View All" button display

  @Output() viewAllClicked = new EventEmitter<void>(); // Event emitter for "View All" button

  walletAddress = '0x12345678987';
  contractAddress = '';
  spinnerMessage = '';

  constructor(
    private readonly sharedService: SharedService,
    private readonly mvtWalletService: MvtWalletService,
    private readonly toastr: ToastrService,
    private readonly router: Router,
    private readonly errorHandler: ErrorHandlerService
  ) { }

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Detect admin context: when accessed via route without stakeholder inputs
    const isAccessedViaRoute = !this.stakeholderWalletAddress && !this.stakeholderName;
    const currentUrl = this.router.url;
    const isAdminFundingDashboard = currentUrl.includes('/funding-dashboard/mvt-transactions');

    if (isAccessedViaRoute && isAdminFundingDashboard) {
      this.showAllTransactions = true;
      console.log('Detected admin funding dashboard context - showing all transactions');
    }

    if (this.stakeholderWalletAddress) {
      this.walletAddress = this.stakeholderWalletAddress;
    }

    this.getMVTBalance();
    this.loadAllTransactions();

    this.sharedService.currentPage
      .pipe(takeUntil(this.destroy$))
      .subscribe(page => {
        this.currentPage = page;
        this.filterTransactionsByType();
      });
  }

  private loadAllTransactions(): void {
    this.isLoading = true;
    this.spinnerMessage = 'Loading transactions...';

    // Determine the address parameter based on context
    let addressToUse: string;

    if (this.showAllTransactions) {
      // Admin "view all" mode - use "all" to fetch all transactions
      addressToUse = 'all';
    } else if (this.stakeholderWalletAddress) {
      // Stakeholder-specific mode - use the provided stakeholder address
      addressToUse = this.stakeholderWalletAddress;
    } else {
      // Default mode - use the default wallet address
      addressToUse = this.walletAddress;
    }

    console.log('Loading transactions with context:', {
      showAllTransactions: this.showAllTransactions,
      stakeholderWalletAddress: this.stakeholderWalletAddress,
      addressToUse: addressToUse,
      transactionLimit: this.transactionLimit
    });

    console.log(`🔍 DEBUG: Calling getMVTTransactionList with limit=${this.transactionLimit} for ${addressToUse}`);

    this.mvtWalletService.getMVTTransactionList(addressToUse, true, this.transactionLimit)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          console.log('Component received response:', response);

          if (response?.data?.getMVTTransactionList?.data) {
            const transactionList = response.data.getMVTTransactionList.data;
            console.log('Raw transaction list from service:', transactionList);

            // Use backend-provided display-ready data directly
            this.allTransactions = this.mapBackendTransactions(transactionList);
            console.log('Mapped transactions:', this.allTransactions);

            // Backend handles context-aware filtering, so no frontend filtering needed
            // Sort by date (backend should already provide sorted data, but ensure consistency)
            this.allTransactions.sort((a, b) => b.date.getTime() - a.date.getTime());

            // Debug: Log final sorted transactions to verify order
            console.log(`🔍 DEBUG: Final sorted transactions (limit=${this.transactionLimit}):`);
            this.allTransactions.slice(0, Math.min(5, this.allTransactions.length)).forEach((tx, index) => {
              console.log(`  ${index + 1}. ID: ${tx.id}, Date: ${tx.date.toISOString()}, Type: ${tx.type}`);
            });

            this.totalItems = this.allTransactions.length;
            this.sharedService.currentPage.next(1);
            this.filterTransactionsByType();

            console.log(`Final loaded transactions count: ${this.allTransactions.length}`);

          } else {
            console.log('No transaction data found in response');
            this.allTransactions = [];
            this.totalItems = 0;
            this.displayTransactions = [];
          }
          this.isLoading = false;
          this.spinnerMessage = '';
        },
        error: (error) => {
          console.error('Component error:', error);
          this.handleTransactionError(error);
          this.isLoading = false;
          this.spinnerMessage = '';
        }
      });
  }

  /**
   * Map backend transactions to frontend interface
   * Backend provides display-ready data, minimal transformation needed
   */
  private mapBackendTransactions(transactions: any[]): Transaction[] {
    if (!transactions?.length) {
      return [];
    }

    return transactions.map((item: any) => ({
      id: item.id,
      date: this.parseDate(item.createdAt ?? item.date ?? item.timestamp),
      type: this.mapDisplayTypeToEnum(item.displayType ?? 'ADDED'),
      originalType: item.transactionType,
      amount: item.amount,
      currency: item.tokenType ? (item.tokenType.toLowerCase() as TransactionType) : TransactionType.MVT,
      status: this.mapTransactionStatus(item.status),
      from: item.fromUserId ?? item.fromWalletId,
      to: item.toUserId ?? item.toWalletId,
      fromUser: item.fromUser,
      toUser: item.toUser,
      adminUser: item.adminUser,
      // Use backend-provided display-ready fields directly
      displayType: item.displayType ?? 'ADDED',
      primaryLabel: item.primaryLabel ?? 'Transaction',
      secondaryInfo: item.secondaryInfo,
      showEtherscanLink: item.showEtherscanLink ?? false,
      formattedDate: item.formattedDate
    }));
  }

  /**
   * Map backend display type to frontend enum
   * @param displayType Backend display type string (SENT, RECEIVED, ADDED)
   * @returns Frontend TransactionOperationType enum (Sent, Received, Added)
   */
  private mapDisplayTypeToEnum(displayType: string): TransactionOperationType {
    if (!displayType) {
      return TransactionOperationType.ADDED;
    }

    switch (displayType.toUpperCase()) {
      case 'SENT':
        return TransactionOperationType.SENT;
      case 'RECEIVED':
        return TransactionOperationType.RECEIVED;
      case 'ADDED':
      case 'MINTED':
      case 'MINT':
        return TransactionOperationType.ADDED;
      default:
        console.warn(`Unknown display type: ${displayType}, defaulting to ADDED`);
        return TransactionOperationType.ADDED;
    }
  }

  private parseDate(dateValue: any): Date {
    if (!dateValue) {
      return new Date();
    }

    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue;
    }

    // If it's a string that looks like an ISO date
    if (typeof dateValue === 'string' && dateValue.includes('T')) {
      return new Date(dateValue);
    }

    // If it's a timestamp (number or string number)
    const timestamp = parseInt(dateValue);
    if (!isNaN(timestamp)) {
      // If timestamp is in seconds, convert to milliseconds
      return new Date(timestamp < 10000000000 ? timestamp * 1000 : timestamp);
    }

    // Fallback to current date
    return new Date();
  }



  filterTransactionsByType(): void {
    let filteredTransactions: Transaction[] = [];

    if (this.currentTransactionType === TransactionType.MVT) {
      filteredTransactions = this.allTransactions.filter(tx => tx.currency === TransactionType.MVT);
    } else if (this.currentTransactionType === TransactionType.USDC) {
      filteredTransactions = this.allTransactions.filter(tx => tx.currency === TransactionType.USDC);
    } else {
      filteredTransactions = this.allTransactions;
    }

    this.totalItems = filteredTransactions.length;
    this.updateDisplayTransactionsFromFiltered(filteredTransactions);
  }

  private updateDisplayTransactionsFromFiltered(filteredTransactions: Transaction[]): void {
    const startIndex = (this.currentPage - 1) * this.ITEMS_PER_PAGE;
    const endIndex = Math.min(startIndex + this.ITEMS_PER_PAGE, filteredTransactions.length);
    this.displayTransactions = filteredTransactions.slice(startIndex, endIndex);
  }

  private getMVTBalance(): void {
    // Skip balance loading when showing all transactions (admin context)
    if (this.showAllTransactions) {
      return;
    }

    this.isLoading = true;
    this.spinnerMessage = 'Loading contract information...';

    const addressToUse = this.stakeholderWalletAddress || this.walletAddress;

    this.mvtWalletService.getMVTBalance(addressToUse, true)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          if (response?.data?.getMVTBalance?.data?.id) {
            this.contractAddress = response.data.getMVTBalance.data.id;
          }
          this.isLoading = false;
          this.spinnerMessage = '';
        },
        error: (error) => {
          console.error('Error loading MVT balance:', error);
          this.isLoading = false;
          this.spinnerMessage = '';
        }
      });
  }

  private handleTransactionError(error: any): void {
    console.error('Transaction error:', error);
    this.allTransactions = [];
    this.totalItems = 0;
    this.displayTransactions = [];

    this.errorHandler.handleError(error, {
      showToast: true,
      severity: ErrorSeverity.MEDIUM
    });
  }



  /**
   * Check if a transaction is an admin minting operation
   * @param transaction The transaction to check
   * @returns true if it's an admin minting transaction
   */
  isAdminMinting(transaction: any): boolean {
    // Check if the original backend transaction type is ADMIN_MINT
    return transaction.originalType === 'ADMIN_MINT';
  }

  /**
   * Get display label for transaction - now uses backend-provided primaryLabel
   * @param transaction The transaction object with display-ready data
   * @returns Display label string
   */
  getTransactionDisplayLabel(transaction: Transaction): string {
    // Use backend-provided primary label if available
    if (transaction.primaryLabel) {
      return transaction.primaryLabel;
    }

    // Fallback for backward compatibility (should not be needed with optimized backend)
    return 'Transaction';
  }

  openInEtherscan(transactionId: string): void {
    this.sharedService.openInEtherscan(transactionId);
  }

  /**
   * Maps API response status string to the TransactionStatus enum value
   */
  private mapTransactionStatus(status: string): TransactionStatus {
    if (!status) return TransactionStatus.COMPLETED;

    switch (status.toUpperCase()) {
      case 'COMPLETED':
      case 'COMPLETE':
      case 'SUCCESS':
      case 'SUCCESSFUL':
        return TransactionStatus.COMPLETED;
      case 'PENDING':
      case 'PROCESSING':
      case 'IN_PROGRESS':
        return TransactionStatus.PENDING;
      case 'FAILED':
      case 'FAILURE':
      case 'ERROR':
        return TransactionStatus.FAILED;
      default:
        return TransactionStatus.COMPLETED;
    }
  }

  formatDate(date: Date): string {
    if (!date) return '';
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'UTC'
    });
  }

  getTitle(): string {
    if (this.showAllTransactions) {
      return 'All MVT Transactions';
    } else if (this.stakeholderName) {
      return `${this.stakeholderName}'s Transactions`;
    } else {
      return 'My Transactions';
    }
  }

  goBack(): void {
    this.router.navigate(['/funding-dashboard']);
  }

  onViewAllTransactions(): void {
    this.viewAllClicked.emit();
  }

  /**
   * Public method to refresh transactions
   * Can be called by parent components after operations like minting
   */
  public refreshTransactions(): void {
    console.log('Refreshing transactions...');
    this.loadAllTransactions();
  }

  getStartIndex(): number {
    return (this.currentPage - 1) * this.ITEMS_PER_PAGE + 1;
  }

  getEndIndex(): number {
    return Math.min(this.currentPage * this.ITEMS_PER_PAGE, this.totalItems);
  }

  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.ITEMS_PER_PAGE);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  }

  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.filterTransactionsByType();
    }
  }

  goToFirstPage(): void {
    this.goToPage(1);
  }

  goToLastPage(): void {
    this.goToPage(this.getTotalPages());
  }

  goToPreviousPage(): void {
    this.goToPage(this.currentPage - 1);
  }

  goToNextPage(): void {
    this.goToPage(this.currentPage + 1);
  }
}
