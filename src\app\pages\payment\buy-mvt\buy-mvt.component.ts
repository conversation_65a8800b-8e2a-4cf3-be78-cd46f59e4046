import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { OnrampSessionService, OnrampSessionData } from '../../../shared/services/onramp-session.service';
import { ErrorHandlerService } from '../../../shared/services/error-handler.service';
import { ErrorSeverity } from '../../../shared/interfaces/standardized-error.interface';

@Component({
  selector: 'app-buy-mvt',
  templateUrl: './buy-mvt.component.html',
  styleUrls: ['./buy-mvt.component.scss']
})
export class BuyMVTComponent implements OnInit {
  purchaseForm: FormGroup;
  loading = false;
  error = '';
  exchangeRate: number | null = null;
  rateLoading = false;

  readonly MIN_AMOUNT = 1;
  readonly MIN_USDC_AMOUNT = 0.000001; // 1 micro USDC
  readonly STRIPE_MIN_AMOUNT = 0.01; // Stripe minimum: $0.01 USD
  readonly STRIPE_MAX_DECIMALS = 2; // Stripe max decimal places for fiat

  constructor(
    private readonly fb: FormBuilder,
    private readonly mvtWalletService: MvtWalletService,
    private onrampSessionService: OnrampSessionService,
    private errorHandler: ErrorHandlerService
  ) {
    this.purchaseForm = this.fb.group({
      mvtAmount: ['', [
        Validators.required,
        Validators.min(this.MIN_AMOUNT),
        Validators.pattern(/^\d+$/)
      ]],
      userId: ['', [
        Validators.required,
        Validators.minLength(3)
      ]]
    });
  }

  ngOnInit(): void {
    console.log('BuyMVTComponent initialized');
    this.fetchExchangeRate();
  }

  fetchExchangeRate(): void {
    this.rateLoading = true;
    this.error = ''; // Clear any previous errors

    // Use MVT wallet service to get exchange rate
    this.mvtWalletService.getMVTWalletExchangeRate().subscribe({
      next: (response) => {
        console.log('Exchange rate response:', response);

        // The API returns currentRate, not exchangeRate
        if (response?.data?.getMVTWalletExchangeRate?.data?.currentRate) {
          this.exchangeRate = response.data.getMVTWalletExchangeRate.data.currentRate;
          console.log('Exchange rate loaded:', this.exchangeRate);
        } else {
          console.warn('Exchange rate not found in response:', response);
          this.exchangeRate = null;
          this.error = 'Exchange rate not available';
        }
        this.rateLoading = false;
      },
      error: (err) => {
        console.error('Error fetching MVT wallet exchange rate:', err);
        this.exchangeRate = null;
        this.rateLoading = false;

        const userMessage = this.errorHandler.handleError(err, {
          showToast: true,
          severity: ErrorSeverity.HIGH
        });

        this.error = userMessage.message;
      }
    });
  }

  onSubmit(): void {
    console.log('Form submission attempted');
    console.log('Form valid:', this.purchaseForm.valid);
    console.log('Form value:', this.purchaseForm.value);
    console.log('Exchange rate:', this.exchangeRate);
    console.log('Form errors:', this.purchaseForm.errors);

    if (this.purchaseForm.invalid || this.exchangeRate === null) {
      console.warn('Form invalid or exchange rate not available');
      if (this.purchaseForm.invalid) {
        console.warn('Form validation errors:', this.purchaseForm.errors);
        Object.keys(this.purchaseForm.controls).forEach(key => {
          const control = this.purchaseForm.get(key);
          if (control && control.invalid) {
            console.warn(`${key} control errors:`, control.errors);
          }
        });
      }
      return;
    }

    this.loading = true;
    this.error = '';

    const { mvtAmount, userId } = this.purchaseForm.value;
    console.log('Starting purchase process:', { mvtAmount, userId, exchangeRate: this.exchangeRate });

    // Calculate USDC amount using the fetched rate
    const mvtAmountNum = Number(mvtAmount);
    const usdcAmountRaw = mvtAmountNum * this.exchangeRate;

    console.log('Calculation details:', {
      mvtAmount: mvtAmountNum,
      exchangeRate: this.exchangeRate,
      usdcAmountRaw: usdcAmountRaw,
      usdcAmountRawFixed2: usdcAmountRaw.toFixed(2),
      usdcAmountRawFixed6: usdcAmountRaw.toFixed(6)
    });

    // For internal calculations, use full precision
    const usdcAmountNum = usdcAmountRaw;

    // For Stripe, we need to round to 2 decimal places
    const stripeCompatibleAmount = this.getStripeCompatibleAmount(usdcAmountNum);

    console.log('Final USDC calculation:', {
      usdcAmountRaw: usdcAmountRaw,
      usdcAmountNum: usdcAmountNum,
      stripeCompatibleAmount: stripeCompatibleAmount,
      isValidAmount: usdcAmountNum > 0,
      isStripeCompatible: this.isStripeCompatible(usdcAmountNum)
    });

    // Validate that the calculated USDC amount is greater than 0
    if (usdcAmountNum <= 0) {
      console.error('Calculated USDC amount is zero or negative:', usdcAmountNum);
      this.error = `Invalid calculation: ${mvtAmountNum} MVT × ${this.exchangeRate} = ${usdcAmountRaw} USDC. Please try a larger amount.`;
      this.loading = false;
      return;
    }

    // Additional validation: minimum USDC amount
    if (usdcAmountNum < this.MIN_USDC_AMOUNT) {
      console.error('Calculated USDC amount is too small:', usdcAmountNum);
      this.error = `Amount too small: ${usdcAmountNum.toFixed(6)} USDC. Minimum is ${this.MIN_USDC_AMOUNT} USDC. Please increase MVT amount.`;
      this.loading = false;
      return;
    }

    // Stripe-specific validation
    if (!this.isStripeCompatible(usdcAmountNum)) {
      console.error('USDC amount not compatible with Stripe requirements:', usdcAmountNum);
      this.error = this.getStripeValidationError(usdcAmountNum);
      this.loading = false;
      return;
    }

    // Create onramp session with user ID for MVT wallet integration
    // Pass all required data: USDC amount, user ID, MVT amount, and exchange rate
    this.mvtWalletService.createOnrampSession(
      stripeCompatibleAmount,
      userId,
      mvtAmountNum.toString(),
      this.exchangeRate.toString()
    )
      .subscribe({
        next: response => {
          console.log('Onramp session response:', response);

          if (response?.data?.createOnrampSession?.data?.clientSecret) {
            const clientSecret = response.data.createOnrampSession.data.clientSecret;
            const sessionId = response.data.createOnrampSession.data.sessionId;
            console.log('Redirecting to crypto-onramp with client secret');

            // Store session data for frontend-triggered transfer
            const sessionData: OnrampSessionData = {
              sessionId: sessionId,
              userId: userId,
              mvtAmount: mvtAmountNum,
              usdcAmount: parseFloat(stripeCompatibleAmount),
              exchangeRate: this.exchangeRate,
              timestamp: Date.now(),
              status: 'pending',
              clientSecret: clientSecret
            };

            this.onrampSessionService.storeSessionData(sessionData);
            console.log('Session data stored for transfer processing:', sessionData);

            // Redirect to crypto-onramp with all needed params including userId for MVT wallet
            window.location.href = `/crypto-onramp?client_secret=${clientSecret}&mvtAmount=${mvtAmount}&userId=${userId}&from=portal`;
          } else {
            console.error('No client secret in response:', response);
            this.error = 'Failed to create payment session - no client secret received';
            this.loading = false;
          }
        },
        error: error => {
          console.error('Error creating payment session:', error);

          const userMessage = this.errorHandler.handleError(error, {
            showToast: true,
            severity: ErrorSeverity.HIGH
          });

          this.error = userMessage.message;
          this.loading = false;
        }
      });
  }

  get f() {
    return this.purchaseForm.controls;
  }

  get displayExchangeRate(): number | null {
    return this.exchangeRate;
  }

  get totalUSDC(): string | null {
    const mvtAmount = Number(this.purchaseForm.get('mvtAmount')?.value);
    if (!this.exchangeRate || isNaN(mvtAmount) || mvtAmount <= 0) return null;

    const usdcAmount = mvtAmount * this.exchangeRate;
    // Show 6 decimal places for accuracy, but remove trailing zeros for display
    const formatted = usdcAmount.toFixed(6);
    return parseFloat(formatted).toString(); // This removes trailing zeros
  }

  get isSubmitDisabled(): boolean {
    const basicValidation = this.purchaseForm.invalid || this.loading || this.exchangeRate === null;

    // Additional validation: check if calculated USDC amount would be valid
    let usdcValidation = false;
    let stripeValidation = false;

    if (!basicValidation) {
      const mvtAmount = Number(this.purchaseForm.get('mvtAmount')?.value);
      if (mvtAmount > 0 && this.exchangeRate) {
        const usdcAmount = mvtAmount * this.exchangeRate;
        usdcValidation = usdcAmount <= 0 || usdcAmount < this.MIN_USDC_AMOUNT;
        stripeValidation = !this.isStripeCompatible(usdcAmount);
      }
    }

    const disabled = basicValidation || usdcValidation || stripeValidation;

    console.log('Submit button disabled:', disabled, {
      formInvalid: this.purchaseForm.invalid,
      loading: this.loading,
      exchangeRateNull: this.exchangeRate === null,
      exchangeRate: this.exchangeRate,
      usdcValidationFailed: usdcValidation,
      stripeValidationFailed: stripeValidation
    });

    return disabled;
  }

  // Helper method for template to parse float values
  parseFloat(value: string): number {
    return parseFloat(value);
  }

  // Get suggested MVT amount for a reasonable USDC amount (e.g., $1 USDC)
  get suggestedMVTAmount(): number | null {
    if (!this.exchangeRate) return null;
    const targetUSDC = 1.0; // Target $1 USDC
    const suggestedMVT = Math.ceil(targetUSDC / this.exchangeRate);
    return suggestedMVT; // No max limit - users can purchase any amount
  }

  // Get minimum MVT amount needed to meet Stripe requirements
  get minimumMVTForStripe(): number | null {
    if (!this.exchangeRate) return null;
    const minMVT = Math.ceil(this.STRIPE_MIN_AMOUNT / this.exchangeRate);
    return minMVT; // No max limit - return the minimum needed
  }

  // Check if calculated USDC amount meets Stripe requirements
  isStripeCompatible(usdcAmount: number): boolean {
    // Must be at least Stripe minimum
    if (usdcAmount < this.STRIPE_MIN_AMOUNT) return false;

    // For Stripe compatibility, we'll round to 2 decimal places
    // Any positive amount >= minimum is acceptable since we can round it
    const rounded = Math.round(usdcAmount * 100) / 100;
    return rounded >= this.STRIPE_MIN_AMOUNT;
  }

  // Get the Stripe-compatible rounded amount
  getStripeCompatibleAmount(usdcAmount: number): string {
    const rounded = Math.round(usdcAmount * 100) / 100;
    return rounded.toFixed(2);
  }

  // Get user-friendly error message for Stripe validation failures
  getStripeValidationError(usdcAmount: number): string {
    if (usdcAmount < this.STRIPE_MIN_AMOUNT) {
      const minMVT = this.minimumMVTForStripe;
      return `Amount too small for Stripe payment processing. Minimum purchase is $${this.STRIPE_MIN_AMOUNT} USDC.${minMVT ? ` Try purchasing at least ${minMVT} MVT tokens.` : ''}`;
    }

    return `Invalid amount format for Stripe. Amount must have maximum ${this.STRIPE_MAX_DECIMALS} decimal places.`;
  }

  // Parse backend error messages for user-friendly display
  parseBackendError(errorMessage: string): string {
    if (errorMessage.includes('source_exchange_amount is not a valid fiat amount')) {
      return `Payment processing error: The calculated amount is too small or improperly formatted for Stripe. Please try a larger MVT amount (minimum ${this.minimumMVTForStripe || 'unknown'} MVT tokens).`;
    }

    if (errorMessage.includes('positive amount up to 2 decimal places')) {
      return `Payment processing error: Amount must be at least $${this.STRIPE_MIN_AMOUNT} with maximum 2 decimal places. Please increase your MVT purchase amount.`;
    }

    return errorMessage; // Return original error if no specific pattern matches
  }
}