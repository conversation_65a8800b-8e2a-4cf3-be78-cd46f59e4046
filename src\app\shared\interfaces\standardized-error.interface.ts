/**
 * Standardized Error Response Interfaces for MVT Wallet Frontend
 * Provides TypeScript types for handling GraphQL-compliant error responses
 */

/**
 * Error categories for standardized error classification
 */
export enum ErrorCategory {
  VALIDATION = 'Validation',
  AUTHORIZATION = 'Authorization',
  DYNAMODB = 'DynamoDB',
  BLOCKCHAIN = 'Blockchain',
  NETWORK = 'Network',
  BUSINESS_LOGIC = 'BusinessLogic',
  SYSTEM = 'System',
  EXTERNAL_SERVICE = 'ExternalService'
}

/**
 * Specific error types within categories
 */
export enum ErrorType {
  // Validation errors
  INVALID_INPUT = 'InvalidInput',
  MISSING_REQUIRED_FIELD = 'MissingRequiredField',
  INVALID_FORMAT = 'InvalidFormat',
  OUT_OF_RANGE = 'OutOfRange',
  
  // Authorization errors
  UNAUTHORIZED = 'Unauthorized',
  FORBIDDEN = 'Forbidden',
  INVALID_TOKEN = 'InvalidToken',
  EXPIRED_SESSION = 'ExpiredSession',
  
  // DynamoDB errors
  DYNAMODB_EXCEPTION = 'DynamoDbException',
  RESOURCE_NOT_FOUND = 'ResourceNotFound',
  CONDITIONAL_CHECK_FAILED = 'ConditionalCheckFailed',
  THROTTLING = 'Throttling',
  
  // Blockchain errors
  TRANSACTION_FAILED = 'TransactionFailed',
  INSUFFICIENT_FUNDS = 'InsufficientFunds',
  CONTRACT_ERROR = 'ContractError',
  NETWORK_ERROR = 'NetworkError',
  
  // Business logic errors
  INSUFFICIENT_BALANCE = 'InsufficientBalance',
  INVALID_OPERATION = 'InvalidOperation',
  RESOURCE_LOCKED = 'ResourceLocked',
  OPERATION_NOT_ALLOWED = 'OperationNotAllowed',
  
  // System errors
  TIMEOUT = 'Timeout',
  SERVICE_UNAVAILABLE = 'ServiceUnavailable',
  INTERNAL_ERROR = 'InternalError',
  CONFIGURATION_ERROR = 'ConfigurationError'
}

/**
 * GraphQL location information
 */
export interface GraphQLLocation {
  line: number;
  column: number;
  sourceName: string | null;
}

/**
 * Standardized error object structure
 */
export interface StandardizedError {
  path: string[];
  data: any;
  errorType: string; // Format: "Category:Type"
  errorInfo: any;
  locations: GraphQLLocation[];
  message: string;
}

/**
 * Complete standardized error response structure
 */
export interface StandardizedErrorResponse {
  data: null;
  errors: StandardizedError[];
}

/**
 * Parsed error type information
 */
export interface ParsedErrorType {
  category: ErrorCategory;
  type: ErrorType;
  fullType: string;
}

/**
 * Error context for business logic errors
 */
export interface ErrorContext {
  operation?: string;
  field?: string;
  requiredRole?: string;
  reason?: string;
  originalError?: string;
  [key: string]: any;
}

/**
 * User-friendly error message mapping
 */
export interface UserFriendlyErrorMessage {
  title: string;
  message: string;
  actionable: boolean;
  retryable: boolean;
  contactSupport: boolean;
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Error handling configuration
 */
export interface ErrorHandlingConfig {
  showToast: boolean;
  logToConsole: boolean;
  reportToAnalytics: boolean;
  severity: ErrorSeverity;
  retryable: boolean;
  fallbackAction?: () => void;
}

/**
 * Legacy error response format (for backward compatibility)
 */
export interface LegacyErrorResponse {
  statusCode: number;
  message: string;
  data: any;
}

/**
 * Union type for all possible error response formats
 */
export type ErrorResponse = StandardizedErrorResponse | LegacyErrorResponse;

/**
 * Type guard to check if response is standardized error format
 */
export function isStandardizedErrorResponse(response: any): response is StandardizedErrorResponse {
  return response && 
         response.data === null && 
         Array.isArray(response.errors) && 
         response.errors.length > 0 &&
         response.errors[0].errorType &&
         response.errors[0].message;
}

/**
 * Type guard to check if response is legacy error format
 */
export function isLegacyErrorResponse(response: any): response is LegacyErrorResponse {
  return response && 
         typeof response.statusCode === 'number' && 
         typeof response.message === 'string' &&
         response.statusCode >= 400;
}

/**
 * Parse error type string into category and type
 */
export function parseErrorType(errorType: string): ParsedErrorType {
  const [category, type] = errorType.split(':');
  return {
    category: category as ErrorCategory,
    type: type as ErrorType,
    fullType: errorType
  };
}

/**
 * Extract the primary error from a standardized error response
 */
export function extractPrimaryError(response: StandardizedErrorResponse): StandardizedError | null {
  return response.errors && response.errors.length > 0 ? response.errors[0] : null;
}

/**
 * Check if error is retryable based on error type
 */
export function isRetryableError(error: StandardizedError): boolean {
  const parsed = parseErrorType(error.errorType);
  
  // Network and system errors are generally retryable
  if (parsed.category === ErrorCategory.NETWORK || 
      parsed.category === ErrorCategory.SYSTEM) {
    return [
      ErrorType.TIMEOUT,
      ErrorType.SERVICE_UNAVAILABLE,
      ErrorType.NETWORK_ERROR,
      ErrorType.THROTTLING
    ].includes(parsed.type);
  }
  
  // DynamoDB throttling is retryable
  if (parsed.category === ErrorCategory.DYNAMODB && 
      parsed.type === ErrorType.THROTTLING) {
    return true;
  }
  
  return false;
}

/**
 * Get error severity based on error type
 */
export function getErrorSeverity(error: StandardizedError): ErrorSeverity {
  const parsed = parseErrorType(error.errorType);
  
  switch (parsed.category) {
    case ErrorCategory.VALIDATION:
      return ErrorSeverity.LOW;
    case ErrorCategory.AUTHORIZATION:
      return ErrorSeverity.MEDIUM;
    case ErrorCategory.BUSINESS_LOGIC:
      return ErrorSeverity.MEDIUM;
    case ErrorCategory.NETWORK:
      return ErrorSeverity.MEDIUM;
    case ErrorCategory.SYSTEM:
    case ErrorCategory.DYNAMODB:
    case ErrorCategory.BLOCKCHAIN:
      return ErrorSeverity.HIGH;
    case ErrorCategory.EXTERNAL_SERVICE:
      return ErrorSeverity.MEDIUM;
    default:
      return ErrorSeverity.MEDIUM;
  }
}
