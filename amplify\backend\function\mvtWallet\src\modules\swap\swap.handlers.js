const swapService = require('./swap.service');
const authService = require('../../shared/services/authService');
const swapValidation = require('./swap.validation');
const { AuthorizationTypes, executeHandler, handleSwapRequestListAuthorization } = require('../../shared/utils/handlerUtils');
const responseUtils = require('../../shared/utils/responseUtils');
const { createLogger, logError } = require('../../shared/utils/logger');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');

/**
 * Handle requestMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleRequestMVTWalletSwap(event, args) {
  try {
    // Get user authentication
    const currentUserDatabaseId = await authService.getCurrentUserDatabaseId(event);
    if (!currentUserDatabaseId) {
      return responseUtils.createUnauthorizedResponse("Authentication required");
    }

    // Validate input
    const validation = swapValidation.validateSwapRequestInput(args.input);
    if (!validation.isValid) {
      return responseUtils.createBadRequestResponse(validation.error);
    }

    const { mvtAmount, description } = args.input;

    const swapRequestData = await swapService.createSwapRequest(
      currentUserDatabaseId,
      mvtAmount,
      description
    );

    return responseUtils.createSuccessResponse(
      swapRequestData,
      `Swap request created successfully for ${mvtAmount} MVT tokens`
    );
  } catch (error) {
    console.error("Error in requestMVTWalletSwap:", error);
    return responseUtils.handleSwapError(error, "create", "requestMVTWalletSwap");
  }
}

/**
 * Handle getMVTWalletSwapRequests request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetMVTWalletSwapRequests(event, args) {
  try {
    const authResult = await handleSwapRequestListAuthorization(event, args);
    if (authResult.error) {
      return authResult.error;
    }

    const { userId, isAdmin } = authResult.context;
    const { limit = 50 } = args;

    const swapRequests = await swapService.getSwapRequestsList(userId, isAdmin, limit);
    return responseUtils.createSuccessResponse(
      swapRequests,
      "Swap requests retrieved successfully"
    );
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'getMVTWalletSwapRequests' });
    logError(logger, error, 'getMVTWalletSwapRequests');
    return responseUtils.handleServiceError(error, "Failed to retrieve swap requests");
  }
}

/**
 * Handle approveMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleApproveMVTWalletSwap(event, args) {
  try {
    // Manual authorization check for better error handling
    const isAuthorized = await authService.checkAdminAuthorization(event);
    if (!isAuthorized) {
      return responseUtils.createForbiddenResponse("Unauthorized: Admin access required");
    }

    // Validate input
    const validation = swapValidation.validateSwapApprovalInput(args.input);
    if (!validation.isValid) {
      return responseUtils.createBadRequestResponse(validation.error);
    }

    const { swapRequestId } = args.input;
    const adminUserId = await authService.getCurrentUserDatabaseId(event);
    if (!adminUserId) {
      return responseUtils.createUnauthorizedResponse("Admin user not found in database");
    }

    const approvalResult = await swapService.approveSwapRequest(swapRequestId, adminUserId);

    return responseUtils.createSuccessResponse(
      approvalResult,
      `Swap request ${swapRequestId} approved successfully`
    );
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'approveMVTWalletSwap' });
    logError(logger, error, 'approveMVTWalletSwap');
    return responseUtils.handleSwapError(error, "approve", "approveMVTWalletSwap");
  }
}

/**
 * Handle rejectMVTWalletSwap request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleRejectMVTWalletSwap(event, args) {
  try {
    // Manual authorization check for better error handling
    const isAuthorized = await authService.checkAdminAuthorization(event);
    if (!isAuthorized) {
      return responseUtils.createForbiddenResponse("Unauthorized: Admin access required");
    }

    // Validate input
    const validation = swapValidation.validateSwapRejectionInput(args.input);
    if (!validation.isValid) {
      return responseUtils.createBadRequestResponse(validation.error);
    }

    const { swapRequestId, rejectionReason } = args.input;
    const adminUserId = await authService.getCurrentUserDatabaseId(event);
    if (!adminUserId) {
      return responseUtils.createUnauthorizedResponse("Admin user not found in database");
    }

    const rejectionResult = await swapService.rejectSwapRequest(
      swapRequestId,
      adminUserId,
      rejectionReason
    );

    return responseUtils.createSuccessResponse(
      rejectionResult,
      `Swap request ${swapRequestId} rejected successfully`
    );
  } catch (error) {
    const logger = createLogger({}, { fieldName: 'rejectMVTWalletSwap' });
    logError(logger, error, 'rejectMVTWalletSwap');
    return responseUtils.handleSwapError(error, "reject", "rejectMVTWalletSwap");
  }
}

module.exports = {
  handleRequestMVTWalletSwap,
  handleGetMVTWalletSwapRequests,
  handleApproveMVTWalletSwap,
  handleRejectMVTWalletSwap
};
