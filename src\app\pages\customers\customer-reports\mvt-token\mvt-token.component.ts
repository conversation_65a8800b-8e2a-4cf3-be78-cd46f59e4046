import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';

import { TransactionType, TokenActiveTab } from 'src/app/shared/types/transaction.types';
import { MvtWalletService } from 'src/app/shared/services/mvt-wallet.service';
import { MvtTransactionsComponent } from '../mvt-transactions/mvt-transactions.component';
import { ErrorHandlerService } from '../../../../shared/services/error-handler.service';
import { ErrorSeverity } from '../../../../shared/interfaces/standardized-error.interface';

@Component({
  selector: 'app-mvt-token',
  templateUrl: './mvt-token.component.html',
  styleUrls: ['./mvt-token.component.scss']
})
export class MvtTokenComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  public readonly TransactionType = TransactionType;
  public readonly TokenActiveTab = TokenActiveTab;

  @ViewChild(MvtTransactionsComponent) mvtTransactionsComponent!: MvtTransactionsComponent;

  private readonly pendingTransactions: Map<string, { amount: number, timestamp: number }> = new Map();

  math = Math;
  mvtBalance = 0;
  usdcBalance = 0;
  mvtTokenAmount: number | null = null;
  usdcAmount: number | null = null;
  usdcWithdrawAmount: number | null = null;
  mvtWithdrawAmount: number | null = null;
  walletAddress = '0x12345678987';
  contractAddress = '';
  usdcContractAddress = '';

  // Enhanced MVT wallet data
  mvtTotalMinted = 0;
  mvtTotalSent = 0;
  mvtLastMintedAt: string | null = null;

  // Enhanced USDC liquidity pool data
  usdcTotalReserves = 0;
  usdcAvailableBalance = 0;
  usdcAdminWalletAddress = '';
  usdcLastUpdated: string | null = null;

  // Exchange rate data
  mvtToUsdcRate = 0;
  exchangeRateLastUpdated: string | null = null;
  isFetchingExchangeRate = false;
  exchangeRateError = false;
  exchangeRateStatus: 'AVAILABLE' | 'FALLBACK_MODE' | 'ERROR_FALLBACK' | 'UNAVAILABLE' | 'ERROR' = 'AVAILABLE';

  isAddingTokens = false;
  isDepositingUSDC = false;
  isWithdrawingUSDC = false;
  isWithdrawingMVT = false;
  isConnectingWallet = false;
  isFetchingMVTBalance = false;
  isFetchingUSDCBalance = false;
  isFetchingMVTWalletData = false;
  isFetchingUSDCPoolData = false;
  isLoading = false;
  isDepositing = false;

  spinnerMessage = '';
  activeTab: TokenActiveTab = TokenActiveTab.MVT;

  showFaqModal = false;

  constructor(
    private readonly toastr: ToastrService,
    private readonly spinner: NgxSpinnerService,
    private readonly router: Router,
    private readonly mvtWalletService: MvtWalletService,
    private readonly errorHandler: ErrorHandlerService
  ) { }

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    this.getMVTBalance();
    this.getUSDCBalance();
    this.getEnhancedMVTWalletData();
    this.getUSDCLiquidityPoolData();
    this.getExchangeRate();
  }

  viewAllTransactions(): void {
    this.router.navigate(['/funding-dashboard/mvt-transactions']);
  }

  navigateToApprovalScreen(): void {
    this.router.navigate(['/funding-dashboard/mvt-swap-approval']);
  }

  fetchMVTBalance(): void {
    this.isFetchingMVTBalance = true;
    this.getMVTBalance();
  }

  fetchUSDCBalance(): void {
    this.isFetchingUSDCBalance = true;
    this.getUSDCBalance();
  }

  fetchExchangeRate(): void {
    this.isFetchingExchangeRate = true;
    this.getExchangeRate();
  }

  /**
   * Get enhanced MVT wallet data including totalMinted and totalSent
   */
  getEnhancedMVTWalletData(): void {
    this.isFetchingMVTWalletData = true;

    this.mvtWalletService.getAdminMVTWalletBalance().subscribe({
      next: (response: any) => {
        if (response?.data?.getAdminMVTWalletBalance?.statusCode === 200) {
          const walletData = response.data.getAdminMVTWalletBalance.data;
          this.mvtTotalMinted = walletData.totalMinted ?? 0;
          this.mvtTotalSent = walletData.totalTransferred ?? 0;
          this.mvtLastMintedAt = walletData.lastMintedAt;

          console.log('Enhanced MVT Wallet Data:', {
            totalMinted: this.mvtTotalMinted,
            totalSent: this.mvtTotalSent,
            lastMintedAt: this.mvtLastMintedAt
          });
        } else {
          console.error('Failed to fetch enhanced MVT wallet data:', response?.data?.getAdminMVTWalletBalance?.message);
          this.toastr.error('Failed to fetch enhanced MVT wallet data');
        }
        this.isFetchingMVTWalletData = false;
      },
      error: (error: any) => {
        console.error('Error fetching enhanced MVT wallet data:', error);
        this.isFetchingMVTWalletData = false;

        this.errorHandler.handleError(error, {
          showToast: true,
          severity: ErrorSeverity.MEDIUM
        });
      }
    });
  }

  /**
   * Get USDC liquidity pool data including totalReserves and availableBalance
   */
  getUSDCLiquidityPoolData(): void {
    this.isFetchingUSDCPoolData = true;

    this.mvtWalletService.getUSDCLiquidityPool().subscribe({
      next: (response: any) => {
        if (response?.data?.getUSDCLiquidityPool?.statusCode === 200) {
          const poolData = response.data.getUSDCLiquidityPool.data;
          this.usdcTotalReserves = poolData.totalReserves ?? 0;
          this.usdcAvailableBalance = poolData.availableBalance ?? 0;
          this.usdcAdminWalletAddress = poolData.adminWalletAddress ?? '';
          this.usdcLastUpdated = poolData.lastUpdated;

          console.log('USDC Liquidity Pool Data:', {
            totalReserves: this.usdcTotalReserves,
            availableBalance: this.usdcAvailableBalance,
            adminWalletAddress: this.usdcAdminWalletAddress,
            lastUpdated: this.usdcLastUpdated
          });
        } else {
          console.error('Failed to fetch USDC liquidity pool data:', response?.data?.getUSDCLiquidityPool?.message);
          this.toastr.error('Failed to fetch USDC liquidity pool data');
        }
        this.isFetchingUSDCPoolData = false;
      },
      error: (error: any) => {
        console.error('Error fetching USDC liquidity pool data:', error);
        this.isFetchingUSDCPoolData = false;
        this.toastr.error('Failed to fetch USDC liquidity pool data');
      }
    });
  }

  /**
   * Get exchange rate data
   */
  getExchangeRate(): void {
    this.isFetchingExchangeRate = true;
    this.exchangeRateError = false;

    this.mvtWalletService.getMVTWalletExchangeRate().subscribe({
      next: (response: any) => {
        const exchangeRateResponse = response?.data?.getMVTWalletExchangeRate;

        if (exchangeRateResponse?.statusCode === 200) {
          // Success response with data
          const exchangeData = exchangeRateResponse.data;

          // The backend returns currentRate (MVT to USDC rate)
          this.mvtToUsdcRate = exchangeData.currentRate ?? 0;
          this.exchangeRateLastUpdated = exchangeData.lastUpdated ?? new Date().toISOString();
          this.exchangeRateError = false;

          // Set status based on backend response
          const status = exchangeData.liquidityStatus?.status;
          this.exchangeRateStatus = status ?? 'AVAILABLE';

          console.log('Exchange Rate Data:', {
            mvtToUsdcRate: this.mvtToUsdcRate,
            lastUpdated: this.exchangeRateLastUpdated,
            status: this.exchangeRateStatus,
            rateDisplay: exchangeData.rateDisplay,
            liquidityStatus: exchangeData.liquidityStatus,
            rawData: exchangeData
          });

          // Show appropriate notifications based on status
          if (status === 'FALLBACK_MODE') {
            console.warn('Exchange rate is in fallback mode');
            this.toastr.warning('Exchange rate is using fallback values due to liquidity issues');
          } else if (status === 'ERROR_FALLBACK') {
            console.warn('Exchange rate is in error fallback mode');
            this.toastr.warning('Exchange rate is using emergency fallback values due to system errors');
          } else if (status === 'UNAVAILABLE') {
            console.warn('Exchange rate is unavailable');
            this.toastr.warning('Exchange rate is currently unavailable');
          } else if (status === 'AVAILABLE') {
            console.log('Exchange rate is available and current');
          }
        } else if (exchangeRateResponse?.statusCode === 500) {
          // Backend returned error response (500 status)
          console.error('Exchange rate service error:', exchangeRateResponse.message);
          this.handleExchangeRateError(exchangeRateResponse.message ?? 'Exchange rate service error');
        } else {
          // Other error responses
          console.error('Unexpected response from exchange rate service:', exchangeRateResponse);
          this.handleExchangeRateError('Unexpected response from exchange rate service');
        }
        this.isFetchingExchangeRate = false;
      },
      error: (error: any) => {
        // Network or GraphQL errors
        console.error('Error fetching exchange rate:', error);
        this.isFetchingExchangeRate = false;

        // Check if it's a GraphQL error with specific message
        if (error.graphQLErrors && error.graphQLErrors.length > 0) {
          const graphQLError = error.graphQLErrors[0];
          this.handleExchangeRateError(`GraphQL Error: ${graphQLError.message}`);
        } else if (error.networkError) {
          this.handleExchangeRateError('Network error: Unable to connect to exchange rate service');
        } else {
          this.handleExchangeRateError('Failed to fetch exchange rate');
        }
      }
    });
  }

  /**
   * Handle exchange rate errors
   */
  private handleExchangeRateError(errorMessage: string): void {
    this.exchangeRateError = true;
    this.exchangeRateStatus = 'ERROR';
    this.mvtToUsdcRate = 0;
    this.exchangeRateLastUpdated = null;
    this.toastr.error(errorMessage);
  }

  /**
   * Check if exchange rate is in a fallback state
   */
  isExchangeRateFallback(): boolean {
    return this.exchangeRateStatus === 'FALLBACK_MODE' || this.exchangeRateStatus === 'ERROR_FALLBACK';
  }

  /**
   * Check if exchange rate is available and live
   */
  isExchangeRateLive(): boolean {
    return this.exchangeRateStatus === 'AVAILABLE' && !this.exchangeRateError;
  }

  /**
   * Get exchange rate status display text
   */
  getExchangeRateStatusText(): string {
    switch (this.exchangeRateStatus) {
      case 'AVAILABLE':
        return 'Live';
      case 'FALLBACK_MODE':
        return 'Fallback';
      case 'ERROR_FALLBACK':
        return 'Emergency';
      case 'UNAVAILABLE':
        return 'Unavailable';
      case 'ERROR':
        return 'Error';
      default:
        return 'Unknown';
    }
  }

  getMVTBalance(): void {
    if (!this.isFetchingMVTBalance) {
      this.isFetchingMVTBalance = true;
    }

    this.mvtWalletService.getMVTBalance(this.walletAddress, true).subscribe({
      next: (response: any) => {
        if (response?.data?.getMVTBalance?.data) {
          const balanceResponse = response.data.getMVTBalance.data;
          if (balanceResponse.balance) {
            this.mvtBalance = balanceResponse.balance;
          } else {
            this.mvtBalance = 0;
          }

          if (balanceResponse.id) {
            this.contractAddress = balanceResponse.id;
          }
        } else {
          this.toastr.error('Failed to fetch MVT balance');
          this.mvtBalance = 0;
        }
        this.isFetchingMVTBalance = false;
      },
      error: (error: any) => {
        this.isFetchingMVTBalance = false;
        this.toastr.error('Failed to fetch MVT balance');
      }
    });
  }

  getUSDCBalance(): void {
    if (!this.isFetchingUSDCBalance) {
      this.isFetchingUSDCBalance = true;
    }

    this.mvtWalletService.getUSDCLiquidityPool().subscribe({
      next: (response: any) => {
        if (response?.data?.getUSDCLiquidityPool?.statusCode === 200) {
          const poolData = response.data.getUSDCLiquidityPool.data;
          // Use totalReserves as the main USDC balance for backward compatibility
          this.usdcBalance = poolData.totalReserves ?? 0;
          this.usdcContractAddress = poolData.adminWalletAddress ?? '';

          console.log('USDC Liquidity Pool Data (for balance):', {
            totalReserves: poolData.totalReserves,
            availableBalance: poolData.availableBalance,
            adminWalletAddress: poolData.adminWalletAddress,
            lastUpdated: poolData.lastUpdated
          });
        } else {
          console.error('Failed to fetch USDC liquidity pool data:', response?.data?.getUSDCLiquidityPool?.message);
          this.toastr.error('Failed to fetch USDC liquidity pool data');
          this.usdcBalance = 0;
        }
        this.isFetchingUSDCBalance = false;
      },
      error: (error: any) => {
        console.error('Error fetching USDC liquidity pool data:', error);
        this.isFetchingUSDCBalance = false;
        this.toastr.error('Failed to fetch USDC liquidity pool data');
      }
    });
  }

  addMVTTokens(): void {
    if (!this.mvtTokenAmount || this.mvtTokenAmount <= 0) {
      this.toastr.warning('Please enter a valid token amount');
      return;
    }

    const now = Date.now();
    const transactionWindow = 30000;
    this.pendingTransactions.forEach((txData, key) => {
      if (now - txData.timestamp > 120000) {
        this.pendingTransactions.delete(key);
      }
    });

    let isDuplicate = false;
    this.pendingTransactions.forEach((txData, key) => {
      if (txData.amount === this.mvtTokenAmount &&
        now - txData.timestamp < transactionWindow) {
        isDuplicate = true;
        this.toastr.info('A similar transaction is already in progress. Please wait.');
      }
    });

    if (isDuplicate) {
      return;
    }

    const transactionId = `mint-${this.mvtTokenAmount}-${now}`;
    this.pendingTransactions.set(transactionId, {
      amount: this.mvtTokenAmount,
      timestamp: now
    });

    console.log(`Minting ${this.mvtTokenAmount} MVT tokens using MVT Wallet backend...`);
    this.isAddingTokens = true;

    this.mvtWalletService.adminMintMVT(this.mvtTokenAmount, `Admin minted ${this.mvtTokenAmount} MVT tokens`).subscribe({
      next: (response: any) => {
        console.log('MVT Mint Response:', response);

        if (response?.data?.adminMintMVT) {
          const mintResponse = response.data.adminMintMVT;
          console.log('Mint Response Details:', mintResponse);

          if (mintResponse.statusCode === 200) {
            this.toastr.success(mintResponse.message ?? 'MVT tokens minted successfully');

            // Log transaction details if available
            if (mintResponse.data) {
              console.log('Mint Transaction Details:', {
                id: mintResponse.data.id,
                transactionHash: mintResponse.data.transactionHash,
                amount: mintResponse.data.amount,
                status: mintResponse.data.status,
                transactionType: mintResponse.data.transactionType
              });
            }

            this.mvtTokenAmount = 0;
            this.getMVTBalance();
            this.getEnhancedMVTWalletData(); // Refresh enhanced data

            // Refresh transactions to show the new mint transaction immediately
            // Add a small delay to ensure backend has processed the transaction
            setTimeout(() => {
              if (this.mvtTransactionsComponent) {
                console.log('Refreshing transactions after successful mint...');
                this.mvtTransactionsComponent.refreshTransactions();
              } else {
                console.warn('MVT Transactions component not found for refresh');
              }
            }, 500); // 500ms delay
          } else {
            console.error('Mint failed with status:', mintResponse.statusCode);
            this.toastr.error(mintResponse.message ?? 'Failed to mint MVT tokens');
          }
        } else {
          console.error('Invalid response structure:', response);
          this.toastr.error('Invalid response from MVT wallet backend');
        }

        this.pendingTransactions.delete(transactionId);
        this.isAddingTokens = false;
      },
      error: (error: any) => {
        console.error('Error minting MVT tokens:', error);
        this.pendingTransactions.delete(transactionId);
        this.isAddingTokens = false;

        // Provide more specific error messages
        const errorMessage = (error?.message ?? error?.error?.message) ?? 'Failed to mint MVT tokens';
        this.toastr.error(errorMessage);
      }
    });
  }

  async depositUSDC(): Promise<void> {
    if (!this.usdcAmount || this.usdcAmount <= 0) {
      this.toastr.warning('Please enter a valid amount');
      return;
    }

    const amountToDeposit = this.usdcAmount;

    this.usdcAmount = 0;

    this.isDepositingUSDC = true;

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Request is taking longer than expected'));
      }, 30000);
    });

    try {
      const depositPromise = new Promise<any>((resolve) => {
        this.mvtWalletService.adminDepositUSDC(amountToDeposit, `Admin deposited ${amountToDeposit} USDC`).subscribe({
          next: (response: any) => {
            resolve(response);
          },
          error: (error: any) => {
            console.error('Error depositing USDC:', error);
            resolve(null);
          }
        });
      });

      const response = await Promise.race([depositPromise, timeoutPromise])
        .catch(error => {
          console.warn('Deposit request timeout:', error);
          this.toastr.info('Your request is still processing. The fund balance will be updated soon.');
          this.isDepositingUSDC = false;
          return null;
        });

      if (response) {
        if (response?.data?.depositUSDC?.statusCode === 200) {
          this.toastr.success(response.data.depositUSDC.message ?? 'USDC deposited successfully');
          this.getUSDCBalance();
          this.getUSDCLiquidityPoolData(); // Refresh enhanced USDC pool data
        } else {
          this.toastr.error(response.data?.depositUSDC?.message ?? 'Failed to deposit USDC');
        }
      }
    } catch (error) {
      console.error('Error in USDC deposit process:', error);
      this.toastr.error('Failed to deposit USDC');
    } finally {
      this.isDepositingUSDC = false;
    }
  }

  /**
   * Withdraws USDC from the contract back to admin wallet using the same private key
   */
  withdrawUSDC(): void {
    if (!this.usdcWithdrawAmount || this.usdcWithdrawAmount <= 0) {
      this.toastr.warning('Please enter a valid amount');
      return;
    }

    if (this.usdcWithdrawAmount > this.usdcBalance) {
      this.toastr.warning('Withdrawal amount exceeds available balance');
      return;
    }

    this.isWithdrawingUSDC = true;

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Request is taking longer than expected'));
      }, 30000);
    });

    const withdrawPromise = new Promise<any>((resolve) => {
      this.mvtWalletService.adminWithdrawUSDC(this.usdcWithdrawAmount, `Admin withdrew ${this.usdcWithdrawAmount} USDC`).subscribe({
        next: (response: any) => {
          resolve(response);
        },
        error: (error: any) => {
          console.error('Error withdrawing USDC:', error);
          resolve(null);
        }
      });
    });

    Promise.race([withdrawPromise, timeoutPromise])
      .then((response) => {
        if (response?.data?.withdrawUSDC?.statusCode === 200) {
          this.toastr.success(response.data.withdrawUSDC.message ?? 'USDC withdrawn successfully');

          this.usdcWithdrawAmount = null;

          this.getUSDCBalance();
          this.getUSDCLiquidityPoolData(); // Refresh enhanced USDC pool data
        } else if (response) {
          this.toastr.error(response.data?.withdrawUSDC?.message ?? 'Failed to withdraw USDC');
        }
      })
      .catch(error => {
        console.warn('Withdraw request timeout:', error);
        this.toastr.info('Your request is still processing. The fund balance will be updated soon.');
      })
      .finally(() => {
        this.isWithdrawingUSDC = false;
      });
  }

  /**
   * Navigate to the crypto onramp page to buy USDC with fiat currency
   */
  buyUSDCWithFiat(): void {
    this.router.navigate(['/payment/buy']);
  }

  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  setActiveTab(tabId: string): void {
    this.activeTab = tabId as TokenActiveTab;
  }

  /**
   * Ensures that the MVT token input only contains positive integer values
   * Removes any decimal part and negative values
   */
  validateIntegerInput(event: any): void {
    const input = event.target;
    let value = input.value;

    if (value.includes('.')) {
      value = value.split('.')[0];
      input.value = value;
    }

    const intValue = parseInt(value, 10);
    if (isNaN(intValue) || intValue < 0) {
      value = '';
      input.value = value;
    }

    this.mvtTokenAmount = intValue > 0 ? intValue : null;
  }

}