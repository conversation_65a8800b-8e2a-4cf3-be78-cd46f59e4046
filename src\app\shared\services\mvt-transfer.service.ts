import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Apollo } from 'apollo-angular';
import {
  PROCESS_ONRAMP_TRANSFER,
  CHECK_ONRAMP_TRANSFER_STATUS,
  VERIFY_ONRAMP_SESSION
} from '../graphql/mvt-wallet-graphql.queries';
import { ErrorHandlerService } from './error-handler.service';
import {
  StandardizedErrorResponse,
  isStandardizedErrorResponse,
  ErrorSeverity
} from '../interfaces/standardized-error.interface';

export interface MVTTransferRequest {
  userId: string;
  mvtAmount: number;
  usdcAmount: number;
  exchangeRate: number;
  sessionId: string;
  description?: string;
}

export interface MVTTransferResponse {
  success: boolean;
  transactionId?: string;
  message: string;
  data?: any;
}

@Injectable({
  providedIn: 'root'
})
export class MvtTransferService {

  constructor(
    private apollo: Apollo,
    private errorHandler: ErrorHandlerService
  ) {}

  /**
   * Process MVT transfer after successful USDC purchase
   */
  processMVTTransfer(transferRequest: MVTTransferRequest): Observable<MVTTransferResponse> {
    console.log('Processing MVT transfer:', transferRequest);

    if (!transferRequest.userId || !transferRequest.mvtAmount || !transferRequest.sessionId) {
      return throwError(() => new Error('Missing required transfer parameters'));
    }



    const variables = {
      userId: transferRequest.userId,
      mvtAmount: transferRequest.mvtAmount,
      usdcAmount: transferRequest.usdcAmount,
      exchangeRate: transferRequest.exchangeRate,
      sessionId: transferRequest.sessionId,
      description: transferRequest.description || `USDC purchase: ${transferRequest.usdcAmount} USDC converted to ${transferRequest.mvtAmount} MVT (rate: ${transferRequest.exchangeRate})`
    };

    return this.apollo.mutate({
      mutation: PROCESS_ONRAMP_TRANSFER,
      variables
    }).pipe(
      map((result: any) => {
        console.log('MVT transfer response:', result);
        
        if (result.data?.processOnrampTransfer?.statusCode === 200) {
          return {
            success: true,
            transactionId: result.data.processOnrampTransfer.data?.mvtTransactionId,
            message: result.data.processOnrampTransfer.message,
            data: result.data.processOnrampTransfer.data
          };
        } else {
          throw new Error(result.data?.processOnrampTransfer?.message || 'Transfer failed');
        }
      }),
      catchError(error => {
        console.error('Error processing MVT transfer:', error);

        // Handle standardized errors
        if (isStandardizedErrorResponse(error)) {
          const userMessage = this.errorHandler.handleError(error, {
            showToast: false, // Let the calling component handle the toast
            logToConsole: true,
            severity: ErrorSeverity.HIGH
          });
          return throwError(() => new Error(userMessage.message));
        }

        // Handle other error formats
        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.HIGH
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }

  /**
   * Verify Stripe onramp session completion
   */
  verifyOnrampSession(sessionId: string): Observable<any> {


    return this.apollo.query({
      query: VERIFY_ONRAMP_SESSION,
      variables: { sessionId },
      fetchPolicy: 'network-only'
    }).pipe(
      map((result: any) => {
        console.log('Onramp session verification:', result);
        return result.data?.verifyOnrampSession;
      }),
      catchError(error => {
        console.error('Error verifying onramp session:', error);

        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.MEDIUM
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }

  /**
   * Check if transfer was already processed for this session
   */
  checkTransferStatus(sessionId: string): Observable<boolean> {


    return this.apollo.query({
      query: CHECK_ONRAMP_TRANSFER_STATUS,
      variables: { sessionId },
      fetchPolicy: 'network-only'
    }).pipe(
      map((result: any) => {
        const response = result.data?.checkOnrampTransferStatus;
        return response?.data?.processed || false;
      }),
      catchError(error => {
        console.error('Error checking transfer status:', error);

        const userMessage = this.errorHandler.handleError(error, {
          showToast: false,
          logToConsole: true,
          severity: ErrorSeverity.MEDIUM
        });

        return throwError(() => new Error(userMessage.message));
      })
    );
  }
}
