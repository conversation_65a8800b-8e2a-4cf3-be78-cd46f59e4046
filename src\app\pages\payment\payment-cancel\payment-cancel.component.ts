import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { OnrampSessionService } from '../../../shared/services/onramp-session.service';

@Component({
  selector: 'app-payment-cancel',
  templateUrl: './payment-cancel.component.html',
  styleUrls: ['./payment-cancel.component.scss']
})
export class PaymentCancelComponent implements OnInit {
  isMVTTransaction = false;
  sessionData: any = null;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly onrampSessionService: OnrampSessionService
  ) { }

  ngOnInit(): void {
    // Check if this was an MVT transaction
    this.route.queryParams.subscribe(params => {
      const from = params['from'];
      if (from === 'portal') {
        this.isMVTTransaction = true;
        this.sessionData = this.onrampSessionService.getSessionData();
      }
    });
  }

  goToDashboard(): void {
    // Clear session data and go to dashboard
    this.onrampSessionService.clearSessionData();
    this.router.navigate(['/dashboard']);
  }

  retryPurchase(): void {
    // Clear session data and retry MVT purchase
    this.onrampSessionService.clearSessionData();
    this.router.navigate(['/payment/buy']);
  }

  retryPayment(): void {
    if (this.isMVTTransaction) {
      this.retryPurchase();
    } else {
      window.history.go(-2);
    }
  }
}