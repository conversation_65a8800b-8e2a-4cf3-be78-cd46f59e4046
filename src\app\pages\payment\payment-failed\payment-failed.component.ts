import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { OnrampSessionService } from '../../../shared/services/onramp-session.service';

@Component({
  selector: 'app-payment-failed',
  templateUrl: './payment-failed.component.html',
  styleUrls: ['./payment-failed.component.scss']
})
export class PaymentFailedComponent implements OnInit {
  isMVTTransaction = false;
  sessionData: any = null;

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly onrampSessionService: OnrampSessionService
  ) {}

  ngOnInit(): void {
    // Check if this was an MVT transaction
    this.route.queryParams.subscribe(params => {
      const from = params['from'];
      if (from === 'portal') {
        this.isMVTTransaction = true;
        this.sessionData = this.onrampSessionService.getSessionData();
      }
    });
  }

  retryPurchase() {
    // Clear any existing session data and redirect to buy-mvt
    this.onrampSessionService.clearSessionData();
    this.router.navigate(['/payment/buy']);
  }

  goToDashboard() {
    // Clear session data and go to dashboard
    this.onrampSessionService.clearSessionData();
    this.router.navigate(['/funding-dashboard']);
  }
}
