import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import {
  StandardizedError,
  StandardizedErrorResponse,
  LegacyErrorResponse,
  ErrorResponse,
  UserFriendlyErrorMessage,
  ErrorHandlingConfig,
  ErrorSeverity,
  ErrorCategory,
  ErrorType,
  isStandardizedErrorResponse,
  isLegacyErrorResponse,
  extractPrimaryError,
  parseErrorType,
  isRetryableError,
  getErrorSeverity
} from '../interfaces/standardized-error.interface';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlerService {

  constructor(private toastr: ToastrService) {}

  /**
   * Handle any error response and display appropriate user message
   */
  handleError(error: any, config?: Partial<ErrorHandlingConfig>): UserFriendlyErrorMessage {
    const defaultConfig: ErrorHandlingConfig = {
      showToast: true,
      logToConsole: true,
      reportToAnalytics: false,
      severity: ErrorSeverity.MEDIUM,
      retryable: false
    };

    const finalConfig = { ...defaultConfig, ...config };

    if (finalConfig.logToConsole) {
      console.error('Error handled by ErrorHandlerService:', error);
    }

    // Handle GraphQL errors
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      return this.handleGraphQLError(error.graphQLErrors[0], finalConfig);
    }

    // Handle network errors
    if (error.networkError) {
      return this.handleNetworkError(error.networkError, finalConfig);
    }

    // Handle standardized error responses
    if (isStandardizedErrorResponse(error)) {
      return this.handleStandardizedError(error, finalConfig);
    }

    // Handle legacy error responses
    if (isLegacyErrorResponse(error)) {
      return this.handleLegacyError(error, finalConfig);
    }

    // Handle raw error objects
    if (error instanceof Error) {
      return this.handleRawError(error, finalConfig);
    }

    // Handle unknown error format
    return this.handleUnknownError(error, finalConfig);
  }

  /**
   * Handle standardized error response
   */
  private handleStandardizedError(response: StandardizedErrorResponse, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const primaryError = extractPrimaryError(response);
    if (!primaryError) {
      return this.createGenericErrorMessage('Unknown error occurred', config);
    }

    const userMessage = this.createUserFriendlyMessage(primaryError);
    const severity = getErrorSeverity(primaryError);
    const retryable = isRetryableError(primaryError);

    if (config.showToast) {
      this.showToast(userMessage, severity);
    }

    return {
      ...userMessage,
      retryable
    };
  }

  /**
   * Handle legacy error response
   */
  private handleLegacyError(response: LegacyErrorResponse, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const userMessage = this.createLegacyUserFriendlyMessage(response);

    if (config.showToast) {
      this.showToast(userMessage, config.severity);
    }

    return userMessage;
  }

  /**
   * Handle GraphQL errors
   */
  private handleGraphQLError(error: any, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const message = error.message || 'GraphQL error occurred';
    const userMessage: UserFriendlyErrorMessage = {
      title: 'Request Error',
      message: this.sanitizeErrorMessage(message),
      actionable: false,
      retryable: true,
      contactSupport: false
    };

    if (config.showToast) {
      this.showToast(userMessage, ErrorSeverity.MEDIUM);
    }

    return userMessage;
  }

  /**
   * Handle network errors
   */
  private handleNetworkError(error: any, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const userMessage: UserFriendlyErrorMessage = {
      title: 'Connection Error',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      actionable: true,
      retryable: true,
      contactSupport: false
    };

    if (config.showToast) {
      this.showToast(userMessage, ErrorSeverity.HIGH);
    }

    return userMessage;
  }

  /**
   * Handle raw error objects
   */
  private handleRawError(error: Error, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const userMessage: UserFriendlyErrorMessage = {
      title: 'Application Error',
      message: this.sanitizeErrorMessage(error.message),
      actionable: false,
      retryable: false,
      contactSupport: true
    };

    if (config.showToast) {
      this.showToast(userMessage, ErrorSeverity.HIGH);
    }

    return userMessage;
  }

  /**
   * Handle unknown error format
   */
  private handleUnknownError(error: any, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    const userMessage: UserFriendlyErrorMessage = {
      title: 'Unexpected Error',
      message: 'An unexpected error occurred. Please try again or contact support if the problem persists.',
      actionable: false,
      retryable: true,
      contactSupport: true
    };

    if (config.showToast) {
      this.showToast(userMessage, ErrorSeverity.HIGH);
    }

    return userMessage;
  }

  /**
   * Create user-friendly message from standardized error
   */
  private createUserFriendlyMessage(error: StandardizedError): UserFriendlyErrorMessage {
    const parsed = parseErrorType(error.errorType);
    
    switch (parsed.category) {
      case ErrorCategory.VALIDATION:
        return {
          title: 'Input Error',
          message: this.sanitizeErrorMessage(error.message),
          actionable: true,
          retryable: false,
          contactSupport: false
        };

      case ErrorCategory.AUTHORIZATION:
        return {
          title: 'Access Denied',
          message: 'You do not have permission to perform this action.',
          actionable: false,
          retryable: false,
          contactSupport: true
        };

      case ErrorCategory.BUSINESS_LOGIC:
        return {
          title: 'Operation Failed',
          message: this.sanitizeErrorMessage(error.message),
          actionable: true,
          retryable: false,
          contactSupport: false
        };

      case ErrorCategory.NETWORK:
      case ErrorCategory.SYSTEM:
        return {
          title: 'System Error',
          message: 'A system error occurred. Please try again in a few moments.',
          actionable: false,
          retryable: true,
          contactSupport: true
        };

      case ErrorCategory.BLOCKCHAIN:
        return {
          title: 'Transaction Error',
          message: this.sanitizeErrorMessage(error.message),
          actionable: true,
          retryable: true,
          contactSupport: false
        };

      default:
        return {
          title: 'Error',
          message: this.sanitizeErrorMessage(error.message),
          actionable: false,
          retryable: false,
          contactSupport: true
        };
    }
  }

  /**
   * Create user-friendly message from legacy error
   */
  private createLegacyUserFriendlyMessage(response: LegacyErrorResponse): UserFriendlyErrorMessage {
    const isClientError = response.statusCode >= 400 && response.statusCode < 500;
    const isServerError = response.statusCode >= 500;

    if (isClientError) {
      return {
        title: 'Request Error',
        message: this.sanitizeErrorMessage(response.message),
        actionable: true,
        retryable: false,
        contactSupport: false
      };
    }

    if (isServerError) {
      return {
        title: 'Server Error',
        message: 'A server error occurred. Please try again later.',
        actionable: false,
        retryable: true,
        contactSupport: true
      };
    }

    return {
      title: 'Error',
      message: this.sanitizeErrorMessage(response.message),
      actionable: false,
      retryable: false,
      contactSupport: true
    };
  }

  /**
   * Create generic error message
   */
  private createGenericErrorMessage(message: string, config: ErrorHandlingConfig): UserFriendlyErrorMessage {
    return {
      title: 'Error',
      message: this.sanitizeErrorMessage(message),
      actionable: false,
      retryable: config.retryable,
      contactSupport: true
    };
  }

  /**
   * Show toast notification based on severity
   */
  private showToast(userMessage: UserFriendlyErrorMessage, severity: ErrorSeverity): void {
    const options = {
      timeOut: this.getToastTimeout(severity),
      closeButton: true,
      progressBar: true
    };

    switch (severity) {
      case ErrorSeverity.LOW:
        this.toastr.info(userMessage.message, userMessage.title, options);
        break;
      case ErrorSeverity.MEDIUM:
        this.toastr.warning(userMessage.message, userMessage.title, options);
        break;
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        this.toastr.error(userMessage.message, userMessage.title, options);
        break;
      default:
        this.toastr.error(userMessage.message, userMessage.title, options);
    }
  }

  /**
   * Get toast timeout based on severity
   */
  private getToastTimeout(severity: ErrorSeverity): number {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 3000;
      case ErrorSeverity.MEDIUM:
        return 5000;
      case ErrorSeverity.HIGH:
        return 7000;
      case ErrorSeverity.CRITICAL:
        return 10000;
      default:
        return 5000;
    }
  }

  /**
   * Sanitize error message for user display
   */
  private sanitizeErrorMessage(message: string): string {
    if (!message) {
      return 'An error occurred';
    }

    // Remove technical details that users don't need to see
    const sanitized = message
      .replace(/DynamoDB/gi, 'database')
      .replace(/GraphQL/gi, 'request')
      .replace(/\b[A-Z]{2,}\b/g, (match) => match.toLowerCase())
      .replace(/\berror\b/gi, '')
      .trim();

    // Ensure first letter is capitalized
    return sanitized.charAt(0).toUpperCase() + sanitized.slice(1);
  }

  /**
   * Check if an error should trigger a retry
   */
  isRetryable(error: any): boolean {
    if (isStandardizedErrorResponse(error)) {
      const primaryError = extractPrimaryError(error);
      return primaryError ? isRetryableError(primaryError) : false;
    }

    if (isLegacyErrorResponse(error)) {
      return error.statusCode >= 500 || error.statusCode === 429; // Server errors and rate limiting
    }

    return false;
  }
}
